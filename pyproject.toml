[project]
name = "maris"
version = "0.1.0"
description = "MARIS - Ship Maneuvering & Autopilot"
authors = [{ name = "MARIS Team" }]
readme = "README.md"
requires-python = ">=3.10"

dependencies = [
  "numpy==2.1.3",
  "scipy>=1.14.0",
  "jsonschema<5",
  "matplotlib==3.*",
]

[project.scripts]
maris = "cli.maris_cli:main"

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["maris", "cli"]