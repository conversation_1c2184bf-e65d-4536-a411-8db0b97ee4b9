{"schema_version": "1.0.0", "$id": "spec/schemas/scenario.schema.json", "identity": {"scenario_id": "turning_circle_demo", "name": "Turning Circle Demo"}, "crs": {"type": "LOCAL_ENU", "origin": {"lon": -0.001, "lat": 51.0, "h": 0.0}, "theta0_rad": 0.0}, "simulation": {"t0": 0.0, "t_end": 60.0, "t_step": 0.5, "integrator": "rk4", "seed": 42}, "initial_conditions": {"state": {"x": 0.0, "y": 0.0, "psi_rad": 0.0, "u": 0.0, "v": 0.0, "r": 0.0}}, "control": {"mode": "manual", "schedule": [{"t": 0.0, "rpm": 20.0, "rudder_rad": 0.1745329252}]}, "environment": {"wind": {"speed": 5.0, "dir_from_rad": 3.141592653589793}, "current": {"speed": 0.5, "dir_to_rad": 0.0}, "bathymetry": {"type": "constant_depth", "depth_m": 30.0}}, "outputs": {"per_tick": {"decimation": 1}, "websocket": {"enabled": false, "decimation": 2}}, "termination_bounds": {"x_min": -5000.0, "x_max": 5000.0, "y_min": -5000.0, "y_max": 5000.0}, "notes": "Demo scenario for scaffolding run"}